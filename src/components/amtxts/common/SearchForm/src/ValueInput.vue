<template>
  <div class="value-input">
    <component
      v-if="currentComponent && !isDisabled"
      :is="currentComponent"
      v-model="inputValue"
      :data="componentData"
      @change="handleValueChange"
      @input="handleInput"
      @selectListEvent="handleSelectListEvent"
      @iconClick="handleIconClick"
    />
    <div v-else-if="isDisabled" class="disabled-input">
      <el-input
        size="small"
        :value="disValue"
        readonly
        disabled
      />
    </div>
  </div>
</template>

<script>
import { getComponentByType, generateComponentData } from '../mixins/fieldTypes'
import { isOperatorDisableInput } from '../mixins/operators'

// 导入Forms组件
import CustInput from '@/components/Forms/components/CustInput.vue'
import CustSelectList from '@/components/Forms/components/CustSelectList/index.vue'
import CustDatePicker from '@/components/Forms/components/CustDatePicker.vue'
import CustInputNumber from '@/components/Forms/components/CustInputNumber.vue'
import CustSelect from '@/components/Forms/components/CustSelect.vue'

export default {
  name: 'ValueInput',
  components: {
    CustInput,
    CustInputNumber,
    CustSelect,
    CustSelectList,
    CustDatePicker
  },
  props: {
    value: {
      type: [String, Number, Array, Object],
      default: null
    },
    fieldConfig: {
      type: Object,
      default: () => ({})
    },
    operator: {
      type: String,
      default: ''
    },
    operatorConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
    }
  },
  computed: {
    inputValue: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    
    currentComponent() {
      if (!this.fieldConfig.component) return CustInput
      return getComponentByType(this.fieldConfig.component)
    },
    
    componentData() {
      return generateComponentData(this.fieldConfig, this.value, this.operator)
    },
    
    isDisabled() {
      return this.operatorConfig.disableInput || isOperatorDisableInput(this.fieldConfig.component, this.operator)
    },
    
    disValue() {
      // return this.operatorConfig.label || this.operator
      return null
    }
  },
  watch: {
    operator: {
      handler(newOperator, oldOperator) {
        // 操作符变化时，如果从非禁用变为禁用，清空值
        const oldDisabled = isOperatorDisableInput(this.fieldConfig.component, oldOperator)
        const newDisabled = isOperatorDisableInput(this.fieldConfig.component, newOperator)
        
        if (!oldDisabled && newDisabled) {
          this.inputValue = null
        }
      }
    },
    
    
  },
  methods: {
    handleValueChange(eventData) {
      // 处理不同组件的change事件格式
      let newValue = eventData
      
      if (eventData && typeof eventData === 'object') {
        if (eventData.field !== undefined ) {
          newValue = eventData.value
        } else if (eventData.code !== undefined ) {
          newValue = eventData.result
        }
      }
      
      // 直接触发事件，不通过 inputValue setter 避免重复触发
      this.$emit('input', newValue)
      this.$emit('value-change', newValue)
    },
    
    handleInput(value) {
      // 直接触发事件，不通过 inputValue setter 避免重复触发
      this.$emit('input', value)
      this.$emit('value-change', value)
    },
    
    handleSelectListEvent(eventData) {
      // 处理快捷查询组件的特殊事件
      this.$emit('selectListEvent', eventData)
    },
    
    handleIconClick(field) {
      // 处理图标点击事件
      this.$emit('iconClick', field)
    },

  },
  
  mounted() {
    
  }
}
</script>

<style scoped>
.value-input {
  flex: 1;
  min-width: 200px;
}

.disabled-input {
  flex: 1;
}

::v-deep .el-input,
::v-deep .el-select,
::v-deep .el-date-editor {
  width: 100%;
}

::v-deep .el-input__inner {
  height: 32px;
  line-height: 32px;
}

::v-deep .el-input__icon{
  line-height: 32px;
}
::v-deep .el-date-editor.el-range-editor.el-input__inner {
  width: 100%;
  padding: 0 10px;
}
</style> 